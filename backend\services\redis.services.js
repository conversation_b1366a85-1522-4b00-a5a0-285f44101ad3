import dotenv from 'dotenv';
dotenv.config();
import Redis from 'ioredis';

const redis = new Redis({
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    password: process.env.REDIS_PASSWORD,
    enableReadyCheck: false, 
});

redis.on('connect', () => {
    console.log('Connected to Redis');
});

redis.on('error', (err) => {
    console.error('Redis connection error:', err.message);
});

redis.on('close', () => {
    console.log('Redis connection closed');
});

export default redis;